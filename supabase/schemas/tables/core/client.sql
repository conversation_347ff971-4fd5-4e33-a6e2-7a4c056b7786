-- Client Table Schema
-- Contains client information and belongs to an organization
-- Client table
CREATE TABLE IF NOT EXISTS "public"."client" (
	"client_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"internal_url" "text",
	"internal_url_description" "text",
	"client_url" "text",
	"created_by_user_id" "uuid" NOT NULL,
	"logo_url" "text",
	"org_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."client" OWNER TO "postgres";

COMMENT ON TABLE "public"."client" IS 'Client contains all the clients of an organization';

-- Primary key constraint
ALTER TABLE ONLY "public"."client"
ADD CONSTRAINT "client_pkey" PRIMARY KEY ("client_id");

-- Unique constraint on name within organization
ALTER TABLE ONLY "public"."client"
ADD CONSTRAINT "client_name_org_id_key" UNIQUE ("name", "org_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."client"
ADD CONSTRAINT "client_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."client"
ADD CONSTRAINT "client_org_id_fkey" FOREIGN KEY ("org_id") REFERENCES "public"."organization" ("org_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Enable Row Level Security
ALTER TABLE "public"."client" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."client" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Row Level Security Policies
CREATE POLICY "Organization editors and admins can insert clients" ON "public"."client" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'organization'::"public"."entity_type",
			"org_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Organization editors and admins can update clients" ON "public"."client"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'organization'::"public"."entity_type",
			"org_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'organization'::"public"."entity_type",
			"org_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Organization admins can delete clients" ON "public"."client" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'organization'::"public"."entity_type",
		"org_id",
		'admin'::"public"."membership_role"
	)
);

CREATE POLICY "Users can view clients they have access to" ON "public"."client" FOR
SELECT
	TO "authenticated" USING (
		(
			"public"."current_user_has_entity_access" ('client'::"public"."entity_type", "client_id")
			OR (
				EXISTS (
					SELECT
						1
					FROM
						"public"."project" "p"
					WHERE
						(
							("p"."client_id" = "client"."client_id")
							AND "public"."current_user_has_entity_access" (
								'project'::"public"."entity_type",
								"p"."project_id"
							)
						)
				)
			)
		)
	);
