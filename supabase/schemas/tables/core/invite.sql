-- Invite Table Schema
-- Contains invitation information for users to join organizations, clients, or projects
-- Invite table
CREATE TABLE IF NOT EXISTS "public"."invite" (
	"invite_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"resource_type" "public"."invite_resource_type" NOT NULL,
	"resource_id" "uuid" NOT NULL,
	"role" "text" NOT NULL,
	"invitee_email" "text" NOT NULL,
	"token_hash" character(64) NOT NULL,
	"status" "public"."invite_status" DEFAULT 'pending'::"public"."invite_status" NOT NULL,
	"inviter_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"updated_by" "uuid",
	"expires_at" timestamp with time zone NOT NULL
);

ALTER TABLE "public"."invite" OWNER TO "postgres";

-- Primary key constraint
ALTER TABLE ONLY "public"."invite"
ADD CONSTRAINT "invite_pkey" PRIMARY KEY ("invite_id");

-- Unique constraint to prevent duplicate invites
ALTER TABLE ONLY "public"."invite"
ADD CONSTRAINT "invite_invitee_email_resource_type_resource_id_key" UNIQUE ("invitee_email", "resource_type", "resource_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."invite"
ADD CONSTRAINT "invite_inviter_id_fkey" FOREIGN KEY ("inviter_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."invite"
ADD CONSTRAINT "invite_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "invite_resource_idx" ON "public"."invite" USING "btree" ("resource_type", "resource_id");

CREATE INDEX "invite_token_hash_idx" ON "public"."invite" USING "btree" ("token_hash")
WHERE
	("status" = 'pending'::"public"."invite_status");

CREATE INDEX "idx_invite_email_status_expires" ON "public"."invite" USING "btree" ("invitee_email", "status", "expires_at");

-- Enable Row Level Security
ALTER TABLE "public"."invite" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."invite" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Row Level Security Policies
CREATE POLICY "Admins and invitees can read invites" ON "public"."invite" FOR
SELECT
	TO "authenticated" USING (
		(
			(
				(
					"resource_type" = 'organization'::"public"."invite_resource_type"
				)
				AND "public"."current_user_has_entity_role" (
					'organization'::"public"."entity_type",
					"resource_id",
					'admin'::"public"."membership_role"
				)
			)
			OR (
				(
					"resource_type" = 'client'::"public"."invite_resource_type"
				)
				AND "public"."current_user_has_entity_role" (
					'client'::"public"."entity_type",
					"resource_id",
					'admin'::"public"."membership_role"
				)
			)
			OR (
				(
					"resource_type" = 'project'::"public"."invite_resource_type"
				)
				AND "public"."can_modify_project" ("resource_id")
			)
			OR (
				EXISTS (
					SELECT
						1
					FROM
						"public"."profile" "p"
					WHERE
						(
							(
								"p"."user_id" = (
									SELECT
										"auth"."uid" () AS "uid"
								)
							)
							AND ("p"."email" = "invite"."invitee_email")
						)
				)
			)
		)
	);

CREATE POLICY "Admins and the invitee can update invites" ON "public"."invite"
FOR UPDATE
	TO "authenticated" USING (
		(
			(
				(
					"resource_type" = 'organization'::"public"."invite_resource_type"
				)
				AND "public"."current_user_has_entity_role" (
					'organization'::"public"."entity_type",
					"resource_id",
					'admin'::"public"."membership_role"
				)
			)
			OR (
				(
					"resource_type" = 'client'::"public"."invite_resource_type"
				)
				AND "public"."current_user_has_entity_role" (
					'client'::"public"."entity_type",
					"resource_id",
					'admin'::"public"."membership_role"
				)
			)
			OR (
				(
					"resource_type" = 'project'::"public"."invite_resource_type"
				)
				AND "public"."can_modify_project" ("resource_id")
			)
			OR (
				EXISTS (
					SELECT
						1
					FROM
						"public"."profile" "p"
					WHERE
						(
							("p"."user_id" = "auth"."uid" ())
							AND ("p"."email" = "invite"."invitee_email")
						)
				)
			)
		)
	)
WITH
	CHECK (
		(
			(
				(
					"resource_type" = 'organization'::"public"."invite_resource_type"
				)
				AND "public"."current_user_has_entity_role" (
					'organization'::"public"."entity_type",
					"resource_id",
					'admin'::"public"."membership_role"
				)
			)
			OR (
				(
					"resource_type" = 'client'::"public"."invite_resource_type"
				)
				AND "public"."current_user_has_entity_role" (
					'client'::"public"."entity_type",
					"resource_id",
					'admin'::"public"."membership_role"
				)
			)
			OR (
				(
					"resource_type" = 'project'::"public"."invite_resource_type"
				)
				AND "public"."can_modify_project" ("resource_id")
			)
			OR (
				EXISTS (
					SELECT
						1
					FROM
						"public"."profile" "p"
					WHERE
						(
							(
								"p"."user_id" = (
									SELECT
										"auth"."uid" () AS "uid"
								)
							)
							AND ("p"."email" = "invite"."invitee_email")
						)
				)
			)
		)
	);

CREATE POLICY "Admins can delete invites" ON "public"."invite" FOR DELETE TO "authenticated" USING (
	(
		(
			(
				"resource_type" = 'organization'::"public"."invite_resource_type"
			)
			AND "public"."current_user_has_entity_role" (
				'organization'::"public"."entity_type",
				"resource_id",
				'admin'::"public"."membership_role"
			)
		)
		OR (
			(
				"resource_type" = 'client'::"public"."invite_resource_type"
			)
			AND "public"."current_user_has_entity_role" (
				'client'::"public"."entity_type",
				"resource_id",
				'admin'::"public"."membership_role"
			)
		)
		OR (
			(
				"resource_type" = 'project'::"public"."invite_resource_type"
			)
			AND "public"."can_modify_project" ("resource_id")
		)
	)
);
