-- Budget Management Functions
-- This file contains functions for managing budget operations, snapshots, and imports
-- Function to create budget snapshots
CREATE OR REPLACE FUNCTION "public"."create_budget_snapshot" (
	"p_project_stage_id" "uuid",
	"p_freeze_reason" "text" DEFAULT NULL::"text"
) RETURNS "uuid" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	v_project_id UUID;
	v_snapshot_id UUID;
	v_item RECORD;
	v_user_id UUID;
BEGIN
	-- Get the current user ID, fallback to a system user if not authenticated
	v_user_id := auth.uid();
	IF v_user_id IS NULL THEN
		-- Use a system user ID for operations not performed by authenticated users
		v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
	END IF;

	-- Get the project_id from the stage
	SELECT project_id INTO v_project_id
	FROM public.project_stage
	WHERE project_stage_id = p_project_stage_id;

	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Project stage not found';
	END IF;

	INSERT INTO public.budget_snapshot (
		project_stage_id,
		freeze_date,
		freeze_reason,
		created_by_user_id
	)
	VALUES (
		p_project_stage_id,
		now(),
		p_freeze_reason,
		v_user_id
	)
	RETURNING budget_snapshot_id INTO v_snapshot_id;
	
	FOR v_item IN (
		SELECT *
		FROM public.budget_line_item_current
		WHERE project_id = v_project_id
	) LOOP
		INSERT INTO public.budget_snapshot_line_item (
			budget_snapshot_id,
			wbs_library_item_id,
			quantity,
			unit,
			material_rate,
			labor_rate,
			productivity_per_hour,
			unit_rate_manual_override,
			unit_rate,
			factor,
			remarks,
			cost_certainty,
			design_certainty
		)
		VALUES (
			v_snapshot_id,
			v_item.wbs_library_item_id,
			v_item.quantity,
			v_item.unit,
			v_item.material_rate,
			v_item.labor_rate,
			v_item.productivity_per_hour,
			v_item.unit_rate_manual_override,
			v_item.unit_rate,
			v_item.factor,
			v_item.remarks,
			v_item.cost_certainty,
			v_item.design_certainty
		);
	END LOOP;
	
	RETURN v_snapshot_id;
END;
$$;

ALTER FUNCTION "public"."create_budget_snapshot" (
	"p_project_stage_id" "uuid",
	"p_freeze_reason" "text"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."create_budget_snapshot" (
	"p_project_stage_id" "uuid",
	"p_freeze_reason" "text"
) IS 'Creates a snapshot of the current budget state for a project stage';

-- Function to revert to a budget snapshot
CREATE OR REPLACE FUNCTION "public"."revert_to_budget_snapshot" (
	"p_budget_snapshot_id" "uuid",
	"p_revert_reason" "text" DEFAULT 'Reverted to snapshot'::"text"
) RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	v_project_id UUID;
	v_item RECORD;
BEGIN
	-- Get the project_id from the snapshot and stage
	SELECT project_id INTO v_project_id
	FROM public.project_stage ps
	JOIN public.budget_snapshot bs ON ps.project_stage_id = bs.project_stage_id
	WHERE bs.budget_snapshot_id = p_budget_snapshot_id;

	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Budget snapshot not found or not linked to a valid project';
	END IF;

	FOR v_item IN (
		SELECT *
		FROM public.budget_snapshot_line_item
		WHERE budget_snapshot_id = p_budget_snapshot_id
	) LOOP
		-- Use the upsert function for each item
		PERFORM public.upsert_budget_line_item(
			v_project_id,
			v_item.wbs_library_item_id,
			v_item.quantity,
			v_item.unit,
			v_item.material_rate,
			v_item.labor_rate,
			v_item.productivity_per_hour,
			v_item.unit_rate_manual_override,
			v_item.unit_rate,
			v_item.factor,
			v_item.remarks,
			v_item.cost_certainty,
			v_item.design_certainty,
			p_revert_reason,
			NULL -- We want to create new records when reverting, not update existing ones
		);
	END LOOP;

	RETURN TRUE;
END;
$$;

ALTER FUNCTION "public"."revert_to_budget_snapshot" (
	"p_budget_snapshot_id" "uuid",
	"p_revert_reason" "text"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."revert_to_budget_snapshot" (
	"p_budget_snapshot_id" "uuid",
	"p_revert_reason" "text"
) IS 'Reverts the current budget to a previous snapshot state';

-- Function to upsert budget line items
CREATE OR REPLACE FUNCTION public.upsert_budget_line_item (
	p_project_id UUID,
	p_wbs_library_item_id UUID,
	p_quantity NUMERIC,
	p_unit TEXT DEFAULT NULL,
	p_material_rate NUMERIC DEFAULT 0,
	p_labor_rate NUMERIC DEFAULT NULL,
	p_productivity_per_hour NUMERIC DEFAULT NULL,
	p_unit_rate_manual_override BOOLEAN DEFAULT FALSE,
	p_unit_rate NUMERIC DEFAULT NULL,
	p_factor NUMERIC DEFAULT NULL,
	p_remarks TEXT DEFAULT NULL,
	p_cost_certainty NUMERIC DEFAULT NULL,
	p_design_certainty NUMERIC DEFAULT NULL,
	p_change_reason TEXT DEFAULT NULL,
	p_budget_line_item_id UUID DEFAULT NULL
) RETURNS UUID LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
DECLARE
    v_calculated_cost NUMERIC;
    v_cost_to_use     NUMERIC;
    v_budget_line_item_id_out UUID;
BEGIN
    -- 1. calculate base cost via helper
    v_calculated_cost := public.calculate_unit_item_cost(
        p_material_rate,
        p_labor_rate,
        p_productivity_per_hour
    );

    -- 2. decide which cost to use
    IF COALESCE(p_unit_rate_manual_override, FALSE) THEN
        IF p_unit_rate IS NULL THEN
            v_cost_to_use := NULL;
        ELSE
            v_cost_to_use := p_unit_rate;
        END IF;
    ELSE
        v_cost_to_use := v_calculated_cost;
    END IF;

    -- 3. single-step UPSERT on the primary key
    INSERT INTO public.budget_line_item_current (
        budget_line_item_id,
        project_id,
        wbs_library_item_id,
        quantity,
        unit,
        material_rate,
        labor_rate,
        productivity_per_hour,
        unit_rate_manual_override,
        unit_rate,
        factor,
        remarks,
        cost_certainty,
        design_certainty,
        updated_at
    )
    VALUES (
        p_budget_line_item_id,
        p_project_id,
        p_wbs_library_item_id,
        p_quantity,
        p_unit,
        p_material_rate,
        p_labor_rate,
        p_productivity_per_hour,
        p_unit_rate_manual_override,
        v_cost_to_use,
        p_factor,
        p_remarks,
        p_cost_certainty,
        p_design_certainty,
        now()
    )
    ON CONFLICT (budget_line_item_id) DO UPDATE
    SET
        project_id              = EXCLUDED.project_id,
        wbs_library_item_id     = EXCLUDED.wbs_library_item_id,
        quantity                = EXCLUDED.quantity,
        unit                    = EXCLUDED.unit,
        material_rate           = EXCLUDED.material_rate,
        labor_rate              = EXCLUDED.labor_rate,
        productivity_per_hour   = EXCLUDED.productivity_per_hour,
        unit_rate_manual_override = EXCLUDED.unit_rate_manual_override,
        unit_rate               = EXCLUDED.unit_rate,
        factor                  = EXCLUDED.factor,
        remarks                 = EXCLUDED.remarks,
        cost_certainty          = EXCLUDED.cost_certainty,
        design_certainty        = EXCLUDED.design_certainty,
        updated_at              = now()
    RETURNING budget_line_item_id
    INTO v_budget_line_item_id_out;

    RETURN v_budget_line_item_id_out;
END;
$$;

ALTER FUNCTION "public"."upsert_budget_line_item" (
	"p_project_id" "uuid",
	"p_wbs_library_item_id" "uuid",
	"p_quantity" numeric,
	"p_unit" "text",
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric,
	"p_unit_rate_manual_override" boolean,
	"p_unit_rate" numeric,
	"p_factor" numeric,
	"p_remarks" "text",
	"p_cost_certainty" numeric,
	"p_design_certainty" numeric,
	"p_change_reason" "text",
	"p_budget_line_item_id" "uuid"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."upsert_budget_line_item" (
	"p_project_id" "uuid",
	"p_wbs_library_item_id" "uuid",
	"p_quantity" numeric,
	"p_unit" "text",
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric,
	"p_unit_rate_manual_override" boolean,
	"p_unit_rate" numeric,
	"p_factor" numeric,
	"p_remarks" "text",
	"p_cost_certainty" numeric,
	"p_design_certainty" numeric,
	"p_change_reason" "text",
	"p_budget_line_item_id" "uuid"
) IS 'Inserts or updates a budget line item with calculated unit rates';

-- Function to calculate unit item cost
CREATE OR REPLACE FUNCTION "public"."calculate_unit_item_cost" (
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric
) RETURNS numeric LANGUAGE "plpgsql"
SET
	"search_path" TO '' AS $$
DECLARE
	v_cost NUMERIC;
BEGIN
	-- Simple calculation for now - can be enhanced later
	v_cost := COALESCE(p_material_rate, 0);

	IF p_labor_rate IS NOT NULL
	AND p_productivity_per_hour IS NOT NULL
	AND p_productivity_per_hour > 0 THEN
		v_cost := v_cost + COALESCE(p_labor_rate, 0) / COALESCE(p_productivity_per_hour, 1);
	END IF;

	RETURN v_cost;
END;
$$;

ALTER FUNCTION "public"."calculate_unit_item_cost" (
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."calculate_unit_item_cost" (
	"p_material_rate" numeric,
	"p_labor_rate" numeric,
	"p_productivity_per_hour" numeric
) IS 'Calculates unit cost based on material rate, labor rate, and productivity';

-- Function to compare budget snapshots
CREATE OR REPLACE FUNCTION "public"."compare_budget_snapshots" (
	"p_snapshot_id_1" "uuid",
	"p_snapshot_id_2" "uuid"
) RETURNS TABLE (
	"wbs_library_item_id" "uuid",
	"snapshot_1_quantity" numeric,
	"snapshot_1_cost" numeric,
	"snapshot_1_factor" numeric,
	"snapshot_2_quantity" numeric,
	"snapshot_2_cost" numeric,
	"snapshot_2_factor" numeric,
	"quantity_diff" numeric,
	"cost_diff" numeric,
	"factor_diff" numeric,
	"percent_change" numeric
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN QUERY
	SELECT
		COALESCE(s1.wbs_library_item_id, s2.wbs_library_item_id) AS wbs_library_item_id,
		s1.quantity AS snapshot_1_quantity,
		s1.unit_rate AS snapshot_1_cost,
		s1.factor AS snapshot_1_factor,
		s2.quantity AS snapshot_2_quantity,
		s2.unit_rate AS snapshot_2_cost,
		s2.factor AS snapshot_2_factor,
		COALESCE(s2.quantity, 0) - COALESCE(s1.quantity, 0) AS quantity_diff,
		COALESCE(s2.unit_rate, 0) - COALESCE(s1.unit_rate, 0) AS cost_diff,
		COALESCE(s2.factor, 0) - COALESCE(s1.factor, 0) AS factor_diff,
		CASE
			WHEN COALESCE(s1.unit_rate, 0) = 0 THEN NULL
			ELSE ((COALESCE(s2.unit_rate, 0) - COALESCE(s1.unit_rate, 0)) / s1.unit_rate) * 100
		END AS percent_change
	FROM public.budget_snapshot_line_item s1
	FULL OUTER JOIN public.budget_snapshot_line_item s2
		ON s1.wbs_library_item_id = s2.wbs_library_item_id
	WHERE s1.budget_snapshot_id = p_snapshot_id_1
	AND s2.budget_snapshot_id = p_snapshot_id_2;
END;
$$;

ALTER FUNCTION "public"."compare_budget_snapshots" (
	"p_snapshot_id_1" "uuid",
	"p_snapshot_id_2" "uuid"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."compare_budget_snapshots" (
	"p_snapshot_id_1" "uuid",
	"p_snapshot_id_2" "uuid"
) IS 'Compares two budget snapshots and returns differences';

-- Function to import budget data
CREATE OR REPLACE FUNCTION public.import_budget_data (p_project_id UUID, p_items JSONB) RETURNS JSONB LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path = '' AS $$
DECLARE
    v_item JSONB;
    v_wbs_code TEXT;
    v_description TEXT;
    v_quantity NUMERIC;
    v_unit TEXT;
    v_material_rate NUMERIC;
    v_factor NUMERIC;
    v_labor_rate NUMERIC;
    v_productivity_per_hour NUMERIC;
    v_unit_rate NUMERIC;
    v_unit_rate_manual_override BOOLEAN;
    v_remarks TEXT;

    v_wbs_library_id UUID;
    v_client_id UUID;
    v_wbs_item_id UUID;
    v_parent_code TEXT;
    v_parent_item_id UUID;
    v_level INTEGER;
    v_in_level_code TEXT;

    v_inserted_count INTEGER := 0;
    v_wbs_created_count INTEGER := 0;
    v_start_time TIMESTAMPTZ;
    v_duration_ms NUMERIC;

    v_wbs_map JSONB := '{}';
BEGIN
    v_start_time := clock_timestamp();

    -- Get project details
    SELECT p.wbs_library_id, p.client_id
    INTO v_wbs_library_id, v_client_id
    FROM public.project p
    WHERE p.project_id = p_project_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Project not found: %', p_project_id;
    END IF;

    -- Check permissions
    IF NOT public.can_modify_project(p_project_id) THEN
        RAISE EXCEPTION 'Insufficient permissions to modify project';
    END IF;

    -- All projects should have a valid wbs_library_id (no null handling needed)
    -- For CostX imports, this will be the Custom WBS library (ID 1)

    -- Process each item (rest of the function remains the same as the original)
    FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
    LOOP
        -- Extract values from JSON
        v_wbs_code := v_item->>'code';
        v_description := v_item->>'description';
        v_quantity := (v_item->>'quantity')::NUMERIC;
        v_unit := v_item->>'unit';
        v_material_rate := (v_item->>'material_rate')::NUMERIC;
        v_factor := CASE WHEN v_item->>'factor' IS NOT NULL THEN (v_item->>'factor')::NUMERIC ELSE NULL END;
        v_labor_rate := CASE WHEN v_item->>'labor_rate' IS NOT NULL THEN (v_item->>'labor_rate')::NUMERIC ELSE NULL END;
        v_productivity_per_hour := CASE WHEN v_item->>'productivity_per_hour' IS NOT NULL THEN (v_item->>'productivity_per_hour')::NUMERIC ELSE NULL END;
        v_unit_rate := CASE WHEN v_item->>'unit_rate' IS NOT NULL THEN (v_item->>'unit_rate')::NUMERIC ELSE NULL END;
        v_unit_rate_manual_override := CASE WHEN v_item->>'unit_rate_manual_override' IS NOT NULL THEN (v_item->>'unit_rate_manual_override')::BOOLEAN ELSE FALSE END;
        v_remarks := v_item->>'remarks';

        -- Validate required fields
        IF v_wbs_code IS NULL OR v_wbs_code = '' THEN
            RAISE EXCEPTION 'WBS code is required for all items';
        END IF;

        IF v_description IS NULL OR v_description = '' THEN
            RAISE EXCEPTION 'Description is required for all items';
        END IF;

        -- Check for duplicate codes in this project
        IF EXISTS (
            SELECT 1 FROM public.wbs_library_item wli
            WHERE wli.code = v_wbs_code
            AND (wli.project_id = p_project_id)
        ) THEN
            RAISE EXCEPTION 'duplicate_code' USING DETAIL = format('WBS code %s already exists in this project', v_wbs_code);
        END IF;

        -- Parse WBS code hierarchy
        WITH wbs_parts AS (
            SELECT string_to_array(v_wbs_code, '.') AS parts
        )
        SELECT
            array_length(parts, 1),
            parts[array_length(parts, 1)],
            CASE
                WHEN array_length(parts, 1) > 1
                THEN array_to_string(parts[1:array_length(parts, 1)-1], '.')
                ELSE NULL
            END
        INTO v_level, v_in_level_code, v_parent_code
        FROM wbs_parts;

        -- Find or create parent WBS item if needed
        v_parent_item_id := NULL;
        IF v_parent_code IS NOT NULL THEN
            -- Check if parent exists in our map first
            IF v_wbs_map ? v_parent_code THEN
                v_parent_item_id := (v_wbs_map->>v_parent_code)::UUID;
            ELSE
                -- Look for existing parent
                SELECT wli.wbs_library_item_id
                INTO v_parent_item_id
                FROM public.wbs_library_item wli
                WHERE wli.code = v_parent_code
                AND wli.wbs_library_id = v_wbs_library_id
                AND (wli.project_id = p_project_id OR wli.client_id = v_client_id OR wli.item_type = 'Standard');

                -- Create parent if it doesn't exist
                IF v_parent_item_id IS NULL THEN
                    WITH parent_parts AS (
                        SELECT string_to_array(v_parent_code, '.') AS parts
                    )
                    INSERT INTO public.wbs_library_item (
                        wbs_library_id,
                        level,
                        in_level_code,
                        parent_item_id,
                        code,
                        description,
                        cost_scope,
                        item_type,
                        client_id,
                        project_id
                    )
                    SELECT
                        v_wbs_library_id,
                        v_level - 1,
                        parts[array_length(parts, 1)],
                        NULL, -- We'd need to recursively create grandparents
                        v_parent_code,
                        'Auto-created parent for ' || v_parent_code,
                        NULL,
                        'Custom',
                        v_client_id,
                        p_project_id
                    FROM parent_parts
                    RETURNING wbs_library_item_id INTO v_parent_item_id;

                    v_wbs_created_count := v_wbs_created_count + 1;
                END IF;

                -- Add to map
                v_wbs_map := v_wbs_map || jsonb_build_object(v_parent_code, v_parent_item_id);
            END IF;
        END IF;

        -- Create WBS library item
        INSERT INTO public.wbs_library_item (
            wbs_library_id,
            level,
            in_level_code,
            parent_item_id,
            code,
            description,
            cost_scope,
            item_type,
            client_id,
            project_id
        ) VALUES (
            v_wbs_library_id,
            v_level,
            v_in_level_code,
            v_parent_item_id,
            v_wbs_code,
            v_description,
            v_description, -- Use description as cost_scope for imported items
            'Custom',
            v_client_id,
            p_project_id
        ) RETURNING wbs_library_item_id INTO v_wbs_item_id;

        v_wbs_created_count := v_wbs_created_count + 1;

        -- Add to map
        v_wbs_map := v_wbs_map || jsonb_build_object(v_wbs_code, v_wbs_item_id);

        -- Create budget line item using existing RPC
        PERFORM public.upsert_budget_line_item(
            p_project_id := p_project_id,
            p_wbs_library_item_id := v_wbs_item_id,
            p_quantity := v_quantity,
            p_unit := v_unit,
            p_material_rate := v_material_rate,
            p_labor_rate := v_labor_rate,
            p_productivity_per_hour := v_productivity_per_hour,
            p_unit_rate_manual_override := v_unit_rate_manual_override,
            p_unit_rate := v_unit_rate,
            p_factor := v_factor,
            p_remarks := v_remarks,
            p_cost_certainty := NULL,
            p_design_certainty := NULL,
            p_change_reason := 'Imported from Excel',
            p_budget_line_item_id := NULL
        );

        v_inserted_count := v_inserted_count + 1;
    END LOOP;

    v_duration_ms := EXTRACT(EPOCH FROM (clock_timestamp() - v_start_time)) * 1000;

    RETURN jsonb_build_object(
        'inserted_count', v_inserted_count,
        'wbs_created_count', v_wbs_created_count,
        'duration_ms', v_duration_ms
    );

EXCEPTION
    WHEN OTHERS THEN
        -- Re-raise the exception to rollback the transaction
        RAISE;
END;
$$;

ALTER FUNCTION "public"."import_budget_data" ("p_project_id" "uuid", "p_items" "jsonb") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."import_budget_data" ("p_project_id" "uuid", "p_items" "jsonb") IS 'Imports budget data from JSON array and returns import results';
