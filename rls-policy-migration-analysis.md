# RLS Policy Migration Analysis

**Migration:** `supabase/schemas/prod.sql` → `supabase/migrations/20250708132044_declarative_schemas.sql`

**Analysis Date:** 2025-07-08

---

## Executive Summary

**Migration Safety Assessment: ⚠️ MODERATE TO HIGH RISK**

The migration involves **24 policy changes** across **9 tables**. While many changes are cosmetic improvements or security enhancements, there are **critical functional changes** that could break existing workflows:

- 🚨 **CRITICAL:** Invite system changes remove invitee access to their own invites
- 🚨 **HIGH IMPACT:** Client management restricted to editors/admins only (removes regular member access)
- ⚠️ **MODERATE:** Simplified access patterns may reduce legitimate access to clients/organizations

**Recommendation: DO NOT PROCEED** without addressing critical issues identified below.

---

## Detailed Policy Changes

### 1. `client` Table - 🚨 CRITICAL SECURITY CHANGE

**Current Policies:**

```sql
-- Allows ANY organization member to create clients
CREATE POLICY "Organization members can insert a clients" ON "public"."client"
FOR INSERT TO "authenticated" WITH CHECK (
    EXISTS (SELECT 1 FROM "public"."membership" "m" WHERE ...)
    AND ("created_by_user_id" = "auth"."uid"())
);

-- Uses broader access function
CREATE POLICY "Organization members can update clients" ON "public"."client"
FOR UPDATE TO "authenticated" USING (
    "public"."current_user_has_entity_access"('organization'::"public"."entity_type", "org_id")
);
```

**Migration Policies:**

```sql
-- Requires editor role or higher
create policy "Organization editors and admins can insert clients" on "public"."client"
for insert to authenticated with check (
    current_user_has_entity_role('organization'::entity_type, org_id, 'editor'::membership_role)
);

-- Requires editor role or higher
create policy "Organization editors and admins can update clients" on "public"."client"
for update to authenticated using (
    current_user_has_entity_role('organization'::entity_type, org_id, 'editor'::membership_role)
);
```

**Impact:** **MORE RESTRICTIVE** - Regular organization members lose ability to create/update clients.

### 2. `invite` Table - 🚨 MAJOR RESTRUCTURING

**Current Policies (Complex Logic):**

```sql
-- Allows admins OR invitees to read invites
CREATE POLICY "Admins and invitees can read invites" ON "public"."invite" FOR SELECT
TO "authenticated" USING (
    -- Admin access for org/client/project OR invitee email match
    (resource_type = 'organization' AND current_user_has_entity_role(...)) OR
    (resource_type = 'client' AND current_user_has_entity_role(...)) OR
    (resource_type = 'project' AND can_modify_project(resource_id)) OR
    (EXISTS (SELECT 1 FROM profile p WHERE p.email = invite.invitee_email))
);
```

**Migration Policies (Simplified):**

```sql
-- Only entity access, NO invitee access
create policy "Users can view invites for entities they have access to" on "public"."invite"
for select to authenticated using (
    current_user_has_entity_access(((resource_type)::text)::entity_type, resource_id)
);

-- Admin-only updates (removes invitee update access)
create policy "Admins can update invites" on "public"."invite"
for update to authenticated using (
    current_user_has_entity_role(((resource_type)::text)::entity_type, resource_id, 'admin'::membership_role)
);
```

**🚨 CRITICAL ISSUES:**

1. **Invitees can no longer view their own invites**
2. **Invitees can no longer update/accept their own invites**
3. **Delete logic changed:** Uses `current_user_has_entity_role('project', ...)` instead of `can_modify_project()` for project invites

### 3. `gateway_checklist_item` Table - ✅ FUNCTIONALLY EQUIVALENT

**Change:** Policy names simplified from "Project Editors and Owners" to "Project editors" but logic remains identical using `can_modify_project()`.

### 4. Budget Tables - ✅ SECURITY IMPROVEMENT

**Enhancement:** Migration adds missing `WITH CHECK` clauses to UPDATE policies:

```sql
-- Migration adds WITH CHECK clause
create policy "Project editors can update budget line item" on "public"."budget_line_item_current" for
update to authenticated using (
	can_modify_project (budget_line_item_current.project_id)
)
with
	check (
		can_modify_project (budget_line_item_current.project_id) -- Added security
	);
```

**Impact:** Prevents privilege escalation during updates.

### 5. View Access Simplification - ⚠️ POTENTIAL ACCESS REDUCTION

**Client View Policy:**

- **Current:** Complex logic allowing access via client membership OR project membership
- **Migration:** Simplified to direct client access only

**Organization View Policy:**

- **Current:** Access via organization, client, OR project membership
- **Migration:** Direct organization access only

**Risk:** Users accessing clients/organizations through project membership may lose access.

---

## Security Impact Analysis

### 🚨 HIGH RISK CHANGES

1. **Invite System Broken** - Invitees lose access to their own invites (breaks core functionality)
2. **Client Management Restriction** - Regular org members lose client creation/update rights
3. **Invite Deletion Logic Changed** - May break project invite deletion workflows

### ⚠️ MODERATE RISK CHANGES

1. **Simplified Access Patterns** - May reduce legitimate access to clients/organizations
2. **Role Requirement Changes** - May require user role adjustments in existing organizations

### ✅ SECURITY IMPROVEMENTS

1. **Added WITH CHECK Clauses** - Prevents privilege escalation in budget updates
2. **Consolidated Audit Policies** - Better organization and maintainability
3. **More Restrictive Client Policies** - Reduces potential for unauthorized client management

---

## Critical Issues Requiring Fixes

### 🛑 MUST FIX BEFORE MIGRATION

#### 1. Restore Invitee Access to Invites

**Problem:** Invitees cannot view or accept their own invites.

**Required Fix:** Modify invite SELECT and UPDATE policies to include invitee email matching:

```sql
-- Fix needed: Add invitee access back
create policy "Users can view invites for entities they have access to" on "public"."invite"
for select to authenticated using (
    current_user_has_entity_access(((resource_type)::text)::entity_type, resource_id)
    OR EXISTS (SELECT 1 FROM profile p WHERE p.user_id = auth.uid() AND p.email = invite.invitee_email)
);
```

#### 2. Fix Project Invite Deletion Logic

**Problem:** Migration changes from `can_modify_project()` to role-based check.

**Required Fix:** Verify project invite deletion still works or restore `can_modify_project()` logic.

#### 3. Verify Client Access Patterns

**Problem:** Simplified client view policy may break existing workflows.

**Required Fix:** Test and potentially restore project-based client access.

---

## Testing Recommendations

### Pre-Migration Testing

1. **Invite Workflows**
   - Create invites for each resource type (org/client/project)
   - Test invitee can view and accept invites
   - Test admin can delete invites

2. **Client Management**
   - Test client creation by regular organization members
   - Verify client updates work for different role levels

3. **Access Patterns**
   - Test organization access through project membership
   - Test client access through project membership

### Post-Migration Validation

1. **Functional Testing**
   - All invite workflows still work
   - Client creation works for appropriate roles
   - Budget updates work correctly

2. **Access Testing**
   - Users retain appropriate access to clients/organizations
   - No unexpected access denials

3. **Security Testing**
   - Verify WITH CHECK clauses prevent privilege escalation
   - Confirm role restrictions work as intended

---

## Migration Recommendations

### 🛑 GO/NO-GO ASSESSMENT: **NO-GO**

**Required Actions Before Migration:**

1. **Fix invite system** - Restore invitee access to their own invites
2. **Verify project invite deletion** - Ensure deletion logic works correctly
3. **Test client access patterns** - Confirm simplified policies don't break workflows
4. **Plan role adjustments** - Prepare to upgrade regular members to editors if needed

### Post-Fix Validation Required:

- [ ] Invite system fully functional
- [ ] Client management works for intended roles
- [ ] No unexpected access loss
- [ ] Security improvements verified

**Only proceed after all critical issues are resolved and testing confirms no functional regressions.**
